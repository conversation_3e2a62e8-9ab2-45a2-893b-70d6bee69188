package cn.sdata.om.al.rpa;

import cn.sdata.om.al.entity.AccountInformation;
import cn.sdata.om.al.entity.NetValueDisclosure;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.ValuationTableRecords;
import cn.sdata.om.al.enums.ValuationDownloadStatus;
import cn.sdata.om.al.enums.ValuationRPAStatus;
import cn.sdata.om.al.service.AccountInformationService;
import cn.sdata.om.al.service.NetValueDisclosureService;
import cn.sdata.om.al.service.ValuationTableRecordsService;
import cn.sdata.om.al.utils.StringUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.JobConstant.*;
import static cn.sdata.om.al.constant.RPAConstant.LOG_ID;

@Component
@AllArgsConstructor
@Slf4j
public class ValuationTableHandler implements BaseHandler{

    private final NetValueDisclosureService netValueDisclosureService;
    private final AccountInformationService accountInformationService;
    private final ValuationTableRecordsService valuationTableRecordsService;

    @Override
    public void execute(Map<String, Object> param, List<RemoteFileInfo> files) {
        log.info("ValuationTableHandler.execute开始执行，处理Excel估值表文件");
        Objects.requireNonNull(files, "RPA返回文件不得为空");
        log.info("文件数量: {}", files.size());
        
        // 记录参数信息
        log.info("执行参数: param={}", param.keySet());
        boolean isAgain = (boolean) param.getOrDefault(IS_AGAIN, false);
        String dataDate = (String) param.get(DATA_DATE);
        String logId = (String) param.get(LOG_ID);
        log.info("执行参数详情: isAgain={}, dataDate={}, logId={}", isAgain, dataDate, logId);
        
        Map<String, String> productNameIds = accountInformationService.list().stream()
                .collect(Collectors.toMap(AccountInformation::getFullProductName, AccountInformation::getId, (oldOne, newOne) -> newOne));
        log.info("productNameIds大小: {}", productNameIds.size());
        
        List<ValuationTableRecords> valuationTableRecordsList = new ArrayList<>();
        Map<String, NetValueDisclosure> netValueMap = getNetValueMap(param);
        log.info("netValueMap大小: {}", netValueMap.size());
        
        // 记录所有产品的当前状态
        if (!netValueMap.isEmpty()) {
            log.info("产品状态详情:");
            netValueMap.forEach((productId, netValueDisclosure) -> {
                log.info("产品ID: {}, 估值日期: {}, rpaStatus: {}, rpaSecondStatus: {}, valuationTablePath: {}, valuationTableDownloaded: {}",
                        productId,
                        netValueDisclosure.getValuationDate(),
                        netValueDisclosure.getRpaStatus(),
                        netValueDisclosure.getRpaSecondStatus(),
                        netValueDisclosure.getValuationTablePath(),
                        netValueDisclosure.getValuationTableDownloaded());
            });
        }
        
        String paramDataDate = (String) param.get(DATA_DATE);
        log.info("原始paramDataDate: {}", paramDataDate);
        
        // 处理不同格式的日期
        String compactDate = paramDataDate.replace("-", "");
        log.info("格式化后的日期: formattedDate={}, compactDate={}", paramDataDate, compactDate);
        
        Collection<NetValueDisclosure> values = netValueMap.values();
        List<String> productIds = new ArrayList<>();
        String matchedDataDate = null;
        
        // 记录文件处理情况
        int matchedFileCount = 0;
        int unmatchedFileCount = 0;
        
        for (RemoteFileInfo file : files) {
            String fileName = file.getFileName();
            log.info("处理文件: {}", fileName);
            
            if (fileName == null) {
                log.warn("文件名为空，跳过");
                continue;
            }
            
            // 检查文件名是否包含任一格式的日期
            boolean containsDate = fileName.contains(paramDataDate) || fileName.contains(compactDate);
            if (!containsDate) {
                log.warn("文件名不包含任何格式的日期，跳过: {}", fileName);
                unmatchedFileCount++;
                continue;
            }
            
            String id = getProductIdByFile(file, productNameIds);
            log.info("提取的产品ID: {}", id);
            
            if (id != null) {
                boolean containsKey = netValueMap.containsKey(id);
                log.info("netValueMap中是否存在键 {}: {}", id, containsKey);
                
                NetValueDisclosure netValueDisclosure = netValueMap.get(id);
                //文件与净值披露记录匹配上了
                if (netValueDisclosure != null) {
                    matchedFileCount++;
                    String valuationDate = netValueDisclosure.getValuationDate();
                    log.info("估值日期: {}", valuationDate);
                    
                    String valuationTablePath = StringUtil.concatSeparator(file.getRelativePath(), fileName);
                    log.info("设置valuationTablePath: {}", valuationTablePath);
                    netValueDisclosure.setValuationTablePath(valuationTablePath);
                    
                    log.info("设置valuationTableDownloaded为SUCCESS");
                    netValueDisclosure.setValuationTableDownloaded(ValuationDownloadStatus.SUCCESS.name());
                    
                    ValuationTableRecords valuationTableRecords = generateRecords(id, valuationDate, file);
                    log.info("生成估值表记录: {}", valuationTableRecords.getId());
                    
                    String updatedValuationTablePath = netValueDisclosure.getValuationTablePath();
                    String balanceTablePath = netValueDisclosure.getBalanceTablePath();
                    log.info("估值表下载情况:{}, 余额表下载情况:{}", updatedValuationTablePath, balanceTablePath);
                    
                    if (updatedValuationTablePath != null || balanceTablePath != null) {
                        matchedDataDate = valuationDate;
                        productIds.add(netValueDisclosure.getProductId());
                        log.info("添加产品ID到列表: {}", netValueDisclosure.getProductId());
                    }
                    
                    valuationTableRecordsList.add(valuationTableRecords);
                } else {
                    unmatchedFileCount++;
                    log.error("未找到匹配的净值披露记录: {}", id);
                }
            } else {
                unmatchedFileCount++;
                log.error("未找到匹配的产品名称: {}", fileName);
            }
        }
        
        log.info("文件处理统计: 总文件数={}, 匹配成功={}, 匹配失败={}", 
                files.size(), matchedFileCount, unmatchedFileCount);
        
        log.info("设置RPA状态为COMPLETED，values大小: {}", values.size());
        if (isAgain) {
            log.info("设置rpaSecondStatus为COMPLETED");
            values.forEach(netValueDisclosure -> {
                String oldStatus = netValueDisclosure.getRpaSecondStatus();
                netValueDisclosure.setRpaSecondStatus(ValuationRPAStatus.COMPLETED.name());
                log.info("产品ID: {}, rpaSecondStatus: {} -> {}", 
                        netValueDisclosure.getProductId(), 
                        oldStatus,
                        netValueDisclosure.getRpaSecondStatus());
            });
        } else {
            log.info("设置rpaStatus为COMPLETED");
            values.forEach(netValueDisclosure -> {
                String oldStatus = netValueDisclosure.getRpaStatus();
                netValueDisclosure.setRpaStatus(ValuationRPAStatus.COMPLETED.name());
                log.info("产品ID: {}, rpaStatus: {} -> {}", 
                        netValueDisclosure.getProductId(), 
                        oldStatus,
                        netValueDisclosure.getRpaStatus());
            });
        }
        
        try {
            log.info("保存更新净值披露批次开始,待更新的数据为:{}", values);
            // 使用增量更新替代全量更新，避免并发冲突
            updateNetValueDisclosureBatch(values, isAgain);
            log.info("保存更新净值披露批次完成");
            
            // 再次查询确认状态是否已更新
            if (!values.isEmpty()) {
                List<String> productIdList = values.stream()
                    .map(NetValueDisclosure::getProductId)
                    .collect(Collectors.toList());
                
                List<NetValueDisclosure> updatedRecords = netValueDisclosureService.getData(productIdList, paramDataDate);
                log.info("状态更新后查询结果: 记录数={}", updatedRecords.size());
                
                updatedRecords.forEach(record -> {
                    log.info("更新后状态: 产品ID={}, rpaStatus={}, rpaSecondStatus={}, valuationTableDownloaded={}",
                            record.getProductId(),
                            record.getRpaStatus(),
                            record.getRpaSecondStatus(),
                            record.getValuationTableDownloaded());
                });
            }
            
            log.info("保存估值表记录批次开始，记录数: {}", valuationTableRecordsList.size());
            valuationTableRecordsService.saveBatch(valuationTableRecordsList);
            log.info("保存估值表记录批次完成");
        } catch (Exception e) {
            log.error("保存更新批次失败: {}", e.getMessage(), e);
        }
        
        if (matchedDataDate != null && !productIds.isEmpty() && !isAgain) {
            log.info("发送托管银行邮件，产品IDs: {}, 日期: {}", productIds, matchedDataDate);
            netValueDisclosureService.sendCustodianBankMail(productIds, matchedDataDate);
        } else {
            if (isAgain) {
                log.info("再次下载模式，不发送托管银行邮件");
            } else {
                log.warn("没有需要发送托管银行邮件的产品");
            }
        }
        
        log.info("ValuationTableHandler.execute执行完成");
    }

    /**
     * 增量更新净值披露记录，只更新必要字段避免并发冲突
     */
    private void updateNetValueDisclosureBatch(Collection<NetValueDisclosure> values, boolean isAgain) {
        for (NetValueDisclosure disclosure : values) {
            try {
                LambdaUpdateWrapper<NetValueDisclosure> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(NetValueDisclosure::getId, disclosure.getId());
                
                // 只更新估值表相关字段
                if (disclosure.getValuationTablePath() != null) {
                    updateWrapper.set(NetValueDisclosure::getValuationTablePath, disclosure.getValuationTablePath());
                }
                if (disclosure.getValuationTableDownloaded() != null) {
                    updateWrapper.set(NetValueDisclosure::getValuationTableDownloaded, disclosure.getValuationTableDownloaded());
                }
                
                // 根据是否二次下载更新对应的RPA状态
                if (isAgain) {
                    if (disclosure.getRpaSecondStatus() != null) {
                        updateWrapper.set(NetValueDisclosure::getRpaSecondStatus, disclosure.getRpaSecondStatus());
                    }
                } else {
                    if (disclosure.getRpaStatus() != null) {
                        updateWrapper.set(NetValueDisclosure::getRpaStatus, disclosure.getRpaStatus());
                    }
                }
                
                netValueDisclosureService.update(updateWrapper);
                log.info("增量更新产品ID: {}, isAgain: {}", disclosure.getProductId(), isAgain);
            } catch (Exception e) {
                log.error("增量更新产品ID: {} 失败: {}", disclosure.getProductId(), e.getMessage(), e);
            }
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public void onFail(Map<String, Object> param) {
        Objects.requireNonNull(param, "RPA参数不得为空");
        log.info("ValuationTableHandler.onFail开始执行");
        
        // 记录参数信息
        log.info("失败处理参数: param={}", param.keySet());
        boolean isAgain = (boolean) param.getOrDefault(IS_AGAIN, false);
        String dataDate = (String) param.get(DATA_DATE);
        String logId = (String) param.get(LOG_ID);
        String errorMsg = (String) param.get("errorMsg");
        log.info("失败处理参数详情: isAgain={}, dataDate={}, logId={}, errorMsg={}", 
                isAgain, dataDate, logId, errorMsg);
        
        List<NetValueDisclosure> list = (List<NetValueDisclosure>) param.get(NET_VALUE_DISCLOSURE_LIST);
        if (list == null || list.isEmpty()) {
            log.warn("没有需要处理的净值披露记录");
            return;
        }
        
        log.info("处理失败的净值披露记录数量: {}", list.size());
        
        // 记录所有产品的当前状态
        log.info("失败处理前产品状态详情:");
        list.forEach(netValueDisclosure -> {
            log.info("产品ID: {}, 估值日期: {}, rpaStatus: {}, rpaSecondStatus: {}, valuationTablePath: {}, valuationTableDownloaded: {}",
                    netValueDisclosure.getProductId(),
                    netValueDisclosure.getValuationDate(),
                    netValueDisclosure.getRpaStatus(),
                    netValueDisclosure.getRpaSecondStatus(),
                    netValueDisclosure.getValuationTablePath(),
                    netValueDisclosure.getValuationTableDownloaded());
        });
        
        if (isAgain) {
            log.info("设置rpaSecondStatus为COMPLETED");
            list.forEach(netValueDisclosure -> {
                String oldStatus = netValueDisclosure.getRpaSecondStatus();
                netValueDisclosure.setRpaSecondStatus(ValuationRPAStatus.COMPLETED.name());
                log.info("产品ID: {}, rpaSecondStatus: {} -> {}", 
                        netValueDisclosure.getProductId(), 
                        oldStatus,
                        netValueDisclosure.getRpaSecondStatus());
            });
        } else {
            log.info("设置rpaStatus为COMPLETED");
            list.forEach(netValueDisclosure -> {
                String oldStatus = netValueDisclosure.getRpaStatus();
                netValueDisclosure.setRpaStatus(ValuationRPAStatus.COMPLETED.name());
                log.info("产品ID: {}, rpaStatus: {} -> {}", 
                        netValueDisclosure.getProductId(), 
                        oldStatus,
                        netValueDisclosure.getRpaStatus());
            });
        }
        
        try {
            log.info("失败保存更新批次开始");
            // 使用增量更新替代全量更新，避免并发冲突
            updateNetValueDisclosureBatch(list, isAgain);
            log.info("保存更新批次完成");
            
            // 再次查询确认状态是否已更新
            if (!list.isEmpty()) {
                List<String> productIdList = list.stream()
                    .map(NetValueDisclosure::getProductId)
                    .collect(Collectors.toList());
                
                List<NetValueDisclosure> updatedRecords = netValueDisclosureService.getData(productIdList, dataDate);
                log.info("状态更新后查询结果: 记录数={}", updatedRecords.size());
                
                updatedRecords.forEach(record -> {
                    log.info("更新后状态: 产品ID={}, rpaStatus={}, rpaSecondStatus={}, valuationTableDownloaded={}",
                            record.getProductId(),
                            record.getRpaStatus(),
                            record.getRpaSecondStatus(),
                            record.getValuationTableDownloaded());
                });
            }
        } catch (Exception e) {
            log.error("保存更新批次失败: {}", e.getMessage(), e);
        }
        
        log.info("ValuationTableHandler.onFail执行完成");
    }

}
